// Data model types for the project planner application

export type Story = {
  id: string;
  name: string;
  background?: string;
  acceptanceCriteria?: string;
  milestoneId: string;
  tags: string[];
  storyPoints?: number;
};

export type Milestone = {
  id: string;
  name: string;
  description: string;
  color: string; // e.g. "yellow", "blue"
  stories: Story[];
};

export type Project = {
  id: string;
  name: string;
  description: string;
  milestones: Milestone[];
};
