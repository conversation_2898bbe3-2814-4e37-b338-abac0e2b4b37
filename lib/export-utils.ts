import { Project, Milestone, Story } from "@/lib/types";

/**
 * Converts a story to YAML format following the issues.yaml structure
 */
function storyToYaml(story: Story, projectName: string, milestoneName: string, indent: string = "  "): string {
  const lines: string[] = [];
  
  lines.push(`${indent}- title: "${story.name}"`);
  
  // Build description from background and acceptance criteria
  let description = "";
  if (story.background) {
    description += `# Background\n${story.background}\n\n`;
  }
  if (story.acceptanceCriteria) {
    description += `# Acceptance Criteria\n${story.acceptanceCriteria}`;
  }
  
  if (description) {
    lines.push(`${indent}  description: |`);
    // Split description into lines and add proper indentation
    const descriptionLines = description.split('\n');
    descriptionLines.forEach(line => {
      lines.push(`${indent}    ${line}`);
    });
    lines.push(''); // Empty line after description
  }
  
  // Add IDs using project and milestone names
  lines.push(`${indent}  teamId: ""`);
  lines.push(`${indent}  stateId: ""`);
  lines.push(`${indent}  projectId: "${projectName}"`);
  lines.push(`${indent}  milestoneId: "${milestoneName}"`);
  
  return lines.join('\n');
}

/**
 * Exports project data to YAML format
 */
export function exportProjectToYaml(project: Project): string {
  const lines: string[] = [];
  
  lines.push('issues:');
  
  // Iterate through all milestones and their stories
  project.milestones.forEach(milestone => {
    milestone.stories.forEach(story => {
      lines.push(storyToYaml(story, project.name, milestone.name));
      lines.push(''); // Empty line between stories
    });
  });
  
  // Remove the last empty line if it exists
  if (lines[lines.length - 1] === '') {
    lines.pop();
  }
  
  return lines.join('\n');
}

/**
 * Triggers a download of the YAML content in the browser
 */
export function downloadYamlFile(yamlContent: string, filename: string = 'project-export.yaml'): void {
  // Create a blob with the YAML content
  const blob = new Blob([yamlContent], { type: 'text/yaml;charset=utf-8' });
  
  // Create a temporary URL for the blob
  const url = URL.createObjectURL(blob);
  
  // Create a temporary anchor element and trigger download
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  // Add to DOM, click, and remove
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL
  URL.revokeObjectURL(url);
}

/**
 * Main export function that combines YAML generation and download
 */
export function exportAndDownloadProject(project: Project, filename?: string): void {
  const yamlContent = exportProjectToYaml(project);
  const exportFilename = filename || `${project.name.toLowerCase().replace(/\s+/g, '-')}-export.yaml`;
  downloadYamlFile(yamlContent, exportFilename);
}
