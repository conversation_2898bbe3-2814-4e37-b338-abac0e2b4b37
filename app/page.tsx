"use client";

import { SignIn<PERSON>utton, SignUp<PERSON><PERSON>on, SignedIn, SignedOut, useUser } from "@clerk/nextjs";
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { Button } from "@/components/ui/button";

export default function LandingPage() {
  const { isSignedIn, isLoaded } = useUser();

  if (!isLoaded) {
    // TODO: Optionally, return a loading spinner or skeleton screen
    return null;
  }

  if (isSignedIn) {
    redirect('/dashboard');
    // redirect() throws an error, so this part might not be strictly necessary,
    // but it's good practice to ensure nothing else renders.
    return null; 
  }
  return (
    <div className="flex flex-col items-center justify-center p-4 md:p-6 bg-background" style={{ minHeight: 'calc(100vh - 4rem)' }}>

      <div className="text-center space-y-6">
        <h1 className="text-5xl font-bold">Welcome to FlowDeck</h1>
        <p className="text-xl text-muted-foreground">
          Your modern solution for agile project planning and story management.
        </p>
        <SignedOut>
          <div className="space-x-4">
            <SignInButton mode="modal">
              <Button size="lg">Sign In</Button>
            </SignInButton>
            <SignUpButton mode="modal">
              <Button variant="outline" size="lg">Sign Up</Button>
            </SignUpButton>
          </div>
        </SignedOut>
      </div>
    </div>
  );
}
