"use client";

import ProjectCard from "@/components/ProjectCard";
import { useQuery, useMutation } from "convex/react";
import { useState } from "react";
import CreateProjectDialog from "@/components/CreateProjectDialog";
import ConfirmDeleteDialog from "@/components/ConfirmDeleteDialog";
import type { Id } from "../../convex/_generated/dataModel";
import { api } from "../../convex/_generated/api";
import { Button } from "@/components/ui/button";
import { PlusCircle } from 'lucide-react';
import { ProjectStatus, ProjectPriority } from "@/types/project";

export default function DashboardPage() {
  const projects = useQuery(api.projects.get);
  const createProjectMutation = useMutation(api.projects.createProject);
  const deleteProjectMutation = useMutation(api.projects.deleteProject);
  const [isCreateProjectDialogOpen, setIsCreateProjectDialogOpen] = useState(false);
  const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState(false);
  const [projectToDeleteId, setProjectToDeleteId] = useState<Id<"projects"> | null>(null);

  return (
    <div className="p-4 md:p-8 bg-background">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">My Projects</h1>
        <Button onClick={() => setIsCreateProjectDialogOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" /> Create New Project
        </Button>
      </div>

      {/* Loading state */}
      {projects === undefined && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">Loading projects...</p>
        </div>
      )}

      {/* Empty state: projects loaded, but there are none */}
      {projects && projects.length === 0 && (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-2">No projects yet!</h2>
          <p className="text-muted-foreground mb-4">Get started by creating your first project board.</p>
          <Button variant="outline" onClick={() => setIsCreateProjectDialogOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" /> Create Your First Project
          </Button>
        </div>
      )}

      {/* Projects loaded and exist */}
      {projects && projects.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {projects.map((project) => (
            <ProjectCard
              key={project._id}
              id={project._id}
              name={project.name}
              description={project.description}
              status={(project.status as ProjectStatus) || "Backlog"}
              priority={(project.priority as ProjectPriority) || "No priority"}
              team={project.team}
              lead={project.lead}
              startDate={project.startDate}
              endDate={project.endDate}
              onDelete={(id) => {
                setProjectToDeleteId(id as Id<"projects">);
                setIsConfirmDeleteDialogOpen(true);
              }}
            />
          ))}
        </div>
      )}

      {/* Error state or unexpected null */}
      {projects === null && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">Could not load projects. Please try again later.</p>
        </div>
      )}
      <CreateProjectDialog
        isOpen={isCreateProjectDialogOpen}
        onOpenChange={setIsCreateProjectDialogOpen}
        onCreateProject={async (values) => {
          await createProjectMutation({
            name: values.name,
            description: values.description,
            status: values.status,
            priority: values.priority,
            team: values.team,
            lead: values.lead,
            startDate: values.startDate,
            endDate: values.endDate,
          });
        }}
      />
      {projectToDeleteId && (
        <ConfirmDeleteDialog
          isOpen={isConfirmDeleteDialogOpen}
          onOpenChange={setIsConfirmDeleteDialogOpen}
          title="Delete Project?"
          description={`Are you sure you want to delete this project? This action cannot be undone.`}
          onConfirm={async () => {
            if (projectToDeleteId) {
              await deleteProjectMutation({ projectId: projectToDeleteId });
              setProjectToDeleteId(null);
              setIsConfirmDeleteDialogOpen(false);
            }
          }}
        />
      )}
    </div>
  );
}
