import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import ProjectBoard from "@/components/ProjectBoard";

import type { Id } from "@/convex/_generated/dataModel"; // Import Id type
import { But<PERSON> } from '@/components/ui/button';

interface ProjectPageProps {
  params: Promise<{ // params is a Promise
    projectId: string;
  }>;
}

export default async function ProjectPage({ params }: ProjectPageProps) { // Mark function as async
  const { projectId } = await params; // Await params before destructuring

  // You can fetch project-specific data here using projectId if needed


  return (
    <div className="p-4 md:p-6 bg-background min-h-screen">
      <div className="flex items-start justify-between mb-2">
        <Link href="/dashboard">
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Dashboard
          </Button>
        </Link>
      </div>
      {/* ProjectDetails component has been removed. Its functionality is now part of ProjectBoard. */}
      {/* The ProjectBoard will eventually also need the projectId to fetch milestones and stories */}
      <ProjectBoard projectId={projectId as Id<"projects">} />
    </div>
  );
}
