export interface Project {
  id: string;
  name: string;
  description?: string;
  status?: ProjectStatus;
  priority?: ProjectPriority;
  team?: string;
  lead?: string;
  startDate?: number; // Unix timestamp in milliseconds
  endDate?: number; // Unix timestamp in milliseconds
}

export type ProjectStatus = 
  | "Backlog"
  | "Planned" 
  | "Paused"
  | "Next up"
  | "Completed"
  | "Canceled";

export const PROJECT_STATUS_OPTIONS: ProjectStatus[] = [
  "Backlog",
  "Planned",
  "Paused", 
  "Next up",
  "Completed",
  "Canceled"
];

export const getStatusColor = (status: ProjectStatus): string => {
  switch (status) {
    case "Backlog":
      return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300";
    case "Planned":
      return "bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300";
    case "Paused":
      return "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300";
    case "Next up":
      return "bg-purple-100 text-purple-700 dark:bg-purple-900/50 dark:text-purple-300";
    case "Completed":
      return "bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300";
    case "Canceled":
      return "bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300";
    default:
      return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300";
  }
};

export type ProjectPriority =
  | "No priority"
  | "Urgent"
  | "High"
  | "Medium"
  | "Low";

export const PROJECT_PRIORITY_OPTIONS: ProjectPriority[] = [
  "No priority",
  "Urgent",
  "High",
  "Medium",
  "Low"
];

export const getPriorityColor = (priority: ProjectPriority): string => {
  switch (priority) {
    case "No priority":
      return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300";
    case "Urgent":
      return "bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300";
    case "High":
      return "bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300";
    case "Medium":
      return "bg-amber-100 text-amber-700 dark:bg-amber-900/50 dark:text-amber-300";
    case "Low":
      return "bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300";
    default:
      return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300";
  }
};
