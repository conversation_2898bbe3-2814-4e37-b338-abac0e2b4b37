"use client";

import { useState, useRef } from "react";
import * as z from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ProjectStatus, PROJECT_STATUS_OPTIONS, ProjectPriority, PROJECT_PRIORITY_OPTIONS } from "@/types/project";

// Define the schema for validation
const projectFormSchema = z.object({
  name: z.string().min(3, {
    message: "Project name must be at least 3 characters.",
  }).max(50, {
    message: "Project name must not be longer than 50 characters.",
  }),
  description: z.string().max(200, {
    message: "Description must not be longer than 200 characters."
  }),
  status: z.enum(["Backlog", "Planned", "Paused", "Next up", "Completed", "Canceled"]).optional().default("Backlog"),
  priority: z.enum(["No priority", "Urgent", "High", "Medium", "Low"]).optional().default("No priority"),
  team: z.string().optional(),
  lead: z.string().optional(),
  startDate: z.number().optional(),
  endDate: z.number().optional(),
}).refine((data) => {
  if (!data.endDate || !data.startDate) return true;
  return data.endDate >= data.startDate;
}, {
  message: "End date must be after start date",
  path: ["endDate"]
});

// Define the type for form values
type ProjectFormValues = z.infer<typeof projectFormSchema>;

// Define props interface
interface CreateProjectDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onCreateProject: (values: ProjectFormValues) => Promise<void>; 
}

export default function CreateProjectDialog({
  isOpen,
  onOpenChange,
  onCreateProject,
}: CreateProjectDialogProps) {
  // State for form values and validation
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [status, setStatus] = useState<ProjectStatus>("Backlog");
  const [priority, setPriority] = useState<ProjectPriority>("No priority");
  const [team, setTeam] = useState("");
  const [lead, setLead] = useState("");
  const [startDate, setStartDate] = useState<number | undefined>();
  const [endDate, setEndDate] = useState<number | undefined>();
  const [nameError, setNameError] = useState<string | null>(null);
  const [descriptionError, setDescriptionError] = useState<string | null>(null);
  const [statusError, setStatusError] = useState<string | null>(null);
  const [priorityError, setPriorityError] = useState<string | null>(null);
  const [teamError, setTeamError] = useState<string | null>(null);
  const [leadError, setLeadError] = useState<string | null>(null);
  const [dateError, setDateError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Reset form function
  const resetForm = () => {
    setName("");
    setDescription("");
    setStatus("Backlog");
    setPriority("No priority");
    setTeam("");
    setLead("");
    setStartDate(undefined);
    setEndDate(undefined);
    setNameError(null);
    setDescriptionError(null);
    setStatusError(null);
    setPriorityError(null);
    setTeamError(null);
    setLeadError(null);
    setDateError(null);
  };
  
  // Validate form data
  const validateForm = (): boolean => {
    let isValid = true;
    setNameError(null);
    setDescriptionError(null);
    setStatusError(null);
    setPriorityError(null);
    
    try {
      projectFormSchema.parse({ name, description, status });
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          if (err.path[0] === "name") {
            setNameError(err.message);
            isValid = false;
          }
          if (err.path[0] === "description") {
            setDescriptionError(err.message);
            isValid = false;
          }
          if (err.path[0] === "status") {
            setStatusError(err.message);
            isValid = false;
          }
          if (err.path[0] === "priority") {
            setPriorityError(err.message);
            isValid = false;
          }
        });
      }
    }
    
    return isValid;
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (validateForm()) {
      setIsSubmitting(true);
      try {
        await onCreateProject({ name, description, status, priority, team, lead, startDate, endDate });
        resetForm();
        onOpenChange(false);
      } catch (error) {
        console.error("Failed to create project:", error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
          <DialogDescription>
            Give your new project a name, description, and status.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-8 py-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Project Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="My Awesome Project"
                className="w-full"
              />
              {nameError && <p className="text-sm text-red-500">{nameError}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="A brief description of what this project is about."
                className="resize-none w-full"
              />
              {descriptionError && <p className="text-sm text-red-500">{descriptionError}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={(value: ProjectStatus) => setStatus(value)}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {PROJECT_STATUS_OPTIONS.map((statusOption) => (
                    <SelectItem key={statusOption} value={statusOption}>
                      {statusOption}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {statusError && <p className="text-sm text-red-500">{statusError}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select value={priority} onValueChange={(value: ProjectPriority) => setPriority(value)}>
                <SelectTrigger id="priority">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {PROJECT_PRIORITY_OPTIONS.map((priorityOption) => (
                    <SelectItem key={priorityOption} value={priorityOption}>
                      {priorityOption}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {priorityError && <p className="text-sm text-red-500">{priorityError}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="team">Team (Optional)</Label>
              <Input
                id="team"
                value={team}
                onChange={(e) => setTeam(e.target.value)}
                placeholder="Project team"
                className="w-full"
              />
              {teamError && <p className="text-sm text-red-500">{teamError}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="lead">Project Lead (Optional)</Label>
              <Input
                id="lead"
                value={lead}
                onChange={(e) => setLead(e.target.value)}
                placeholder="Project lead"
                className="w-full"
              />
              {leadError && <p className="text-sm text-red-500">{leadError}</p>}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date (Optional)</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate ? new Date(startDate).toISOString().split('T')[0] : ''}
                  onChange={(e) => setStartDate(e.target.value ? new Date(e.target.value).getTime() : undefined)}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">End Date (Optional)</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate ? new Date(endDate).toISOString().split('T')[0] : ''}
                  onChange={(e) => setEndDate(e.target.value ? new Date(e.target.value).getTime() : undefined)}
                  className="w-full"
                />
              </div>
              {dateError && <p className="col-span-2 text-sm text-red-500">{dateError}</p>}
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => {
                resetForm();
                onOpenChange(false);
              }} 
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Project"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
