import Link from 'next/link';
import { useState } from 'react';
import { <PERSON>, <PERSON>H<PERSON>er, CardT<PERSON>le, CardDescription, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Trash2, Pencil, Users, User, Calendar } from 'lucide-react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import ProjectEditDialog from './ProjectEditDialog';
import { ProjectStatus, getStatusColor, ProjectPriority, getPriorityColor } from '@/types/project';

export interface ProjectCardProps {
  id: Id<"projects">;
  name: string;
  description?: string;
  status?: ProjectStatus;
  priority?: ProjectPriority;
  team?: string;
  lead?: string;
  startDate?: number;
  endDate?: number;
  onDelete: (id: Id<"projects">) => void;
}

export default function ProjectCard({ 
  id, 
  name, 
  description, 
  status = "Backlog", 
  priority = "No priority",
  team,
  lead,
  startDate,
  endDate,
  onDelete 
}: ProjectCardProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const performUpdateProjectDetails = useMutation(api.projects.updateProjectDetails);

  const handleEditClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsEditDialogOpen(true);
  };

  const handleUpdateProject = async (
    newName: string,
    newDescription: string,
    newStatus: ProjectStatus,
    newPriority: ProjectPriority,
    newTeam?: string,
    newLead?: string,
    newStartDate?: number,
    newEndDate?: number
  ) => {
    setIsUpdating(true);
    try {
      await performUpdateProjectDetails({
        projectId: id,
        name: newName,
        description: newDescription,
        status: newStatus,
        priority: newPriority,
        team: newTeam,
        lead: newLead,
        startDate: newStartDate,
        endDate: newEndDate,
      });
    } catch (error) {
      console.error("Failed to update project from card:", error);
      // TODO: Show toast notification for error
    } finally {
      setIsUpdating(false);
      // Dialog will close itself via onOpenChange after onUpdate completes
    }
  };

  return (
    <>
      <Link href={`/project/${id}`} className="block hover:shadow-lg transition-shadow duration-200 ease-in-out rounded-2xl">
        <Card className="h-full rounded-2xl hover:border-primary flex flex-col group">
          <CardHeader className="relative px-4 py-1">
            {/* Header section with title and status/priority labels */}
            <div className="flex items-start justify-between mb-3">
              <CardTitle className="text-lg font-bold text-left flex-1 pr-4 line-clamp-1">
                {name}
              </CardTitle>
              <div className="flex items-center gap-2 flex-shrink-0">
                <span className={`px-2 py-1 rounded-md text-xs font-medium ${getStatusColor(status)}`}>
                  {status}
                </span>
                <span className={`px-2 py-1 rounded-md text-xs font-medium ${getPriorityColor(priority)}`}>
                  {priority}
                </span>
              </div>
            </div>
            
            {/* Description */}
            {description && (
              <CardDescription className="text-sm text-muted-foreground mb-3 text-left line-clamp-2">
                {description}
              </CardDescription>
            )}
            
            {/* Team information and action buttons */}
            <div className="flex items-center justify-between">
              {(team || lead || startDate) && (
                <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                  {team && (
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      <span>Team: {team}</span>
                    </div>
                  )}
                  {lead && (
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      <span>Lead: {lead}</span>
                    </div>
                  )}
                  {startDate && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>Start: {new Date(startDate).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
              )}
              
              {/* Edit and Delete buttons */}
              <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity ml-auto">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-muted-foreground hover:text-primary h-6 w-6"
                  onClick={handleEditClick}
                  aria-label="Edit project"
                >
                  <Pencil className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-muted-foreground hover:text-destructive h-6 w-6"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onDelete(id);
                  }}
                  aria-label="Delete project"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
            
          </CardHeader>
          <CardContent className="pt-0 pb-2 px-4 flex-grow">
          </CardContent>
        </Card>
      </Link>
      {isEditDialogOpen && (
        <ProjectEditDialog
          projectName={name}
          projectDescription={description || ""}
          projectStatus={status}
          projectPriority={priority}
          projectTeam={team}
          projectLead={lead}
          projectStartDate={startDate}
          projectEndDate={endDate}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onUpdate={handleUpdateProject}
          isUpdating={isUpdating}
        />
      )}
    </>
  );
}
