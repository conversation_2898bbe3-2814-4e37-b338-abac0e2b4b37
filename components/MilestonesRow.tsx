"use client";

import { Milestone } from "@/lib/types";
import MilestoneCard from "@/components/MilestoneCard";
import { <PERSON>ton } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

interface MilestonesRowProps {
  milestones: Milestone[];
  onAddMilestone: () => void;
  onUpdateMilestone: (milestoneId: string, updates: Partial<Milestone>) => void;
  onDeleteMilestone: (milestoneId: string) => void;
}

export default function MilestonesRow({
  milestones,
  onAddMilestone,
  onUpdateMilestone,
  onDeleteMilestone,
}: MilestonesRowProps) {
  return (
    <div className="w-full pb-4">
      <div className="flex gap-6 py-2 px-2">
        {milestones.map((milestone) => (
          <MilestoneCard
            key={milestone.id}
            milestone={milestone}
            onUpdate={(updates) => onUpdateMilestone(milestone.id, updates)}
            onDelete={() => onDeleteMilestone(milestone.id)}
          />
        ))}
        <div className="flex items-center justify-center">
          <Button
            onClick={onAddMilestone}
            variant="outline"
            className="h-full min-w-[280px] sm:min-w-[320px] md:min-w-[360px] lg:min-w-[400px] rounded-2xl border-dashed border-2 flex flex-col items-center justify-center p-4 hover:border-primary hover:bg-primary/5 transition-all"
          >
            <PlusCircle className="h-6 w-6 mb-2" />
            <span className="text-base">Add Milestone</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
