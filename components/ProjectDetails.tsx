"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import type { Id } from "@/convex/_generated/dataModel";
import { Skeleton } from "@/components/ui/skeleton";

interface ProjectDetailsProps {
  projectId: Id<"projects">;
}

export default function ProjectDetails({ projectId }: ProjectDetailsProps) {
  const project = useQuery(api.projects.getById, { projectId });

  if (project === undefined) {
    // Loading state
    return (
      <div className="space-y-2 mb-6">
        <Skeleton className="h-8 w-3/4" />
        <Skeleton className="h-6 w-1/2" />
      </div>
    );
  }

  if (project === null) {
    // Project not found
    return (
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-destructive">Project Not Found</h1>
        <p className="text-muted-foreground">The requested project could not be found or you may not have permission to view it.</p>
      </div>
    );
  }

  return (
    <div className="mb-6">
      <h1 className="text-3xl font-bold tracking-tight break-words">{project.name}</h1>
      {project.description && (
        <p className="text-lg text-muted-foreground mt-1 break-words">{project.description}</p>
      )}
    </div>
  );
}
