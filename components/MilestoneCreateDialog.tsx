"use client";

import { useState, useEffect } from "react";
import { Milestone } from "@/lib/types";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface MilestoneCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreate: (milestone: Omit<Milestone, "id" | "stories">) => void;
}

export default function MilestoneCreateDialog({
  open,
  onOpenChange,
  onCreate,
}: MilestoneCreateDialogProps) {
  const [newMilestone, setNewMilestone] = useState<Omit<Milestone, "id" | "stories">>({
    name: "",
    description: "",
    color: "blue",
  });

  // Reset form when dialog opens or reopens
  useEffect(() => {
    if (open) {
      // Reset form to defaults
      setNewMilestone({
        name: "",
        description: "",
        color: "blue",
      });
    }
  }, [open]);

  const handleOpenChange = (open: boolean) => {
    onOpenChange(open);
  };

  // Define a wider assortment of colors
  const colorOptions = [
    { name: "Red", value: "red" },
    { name: "Pink", value: "pink" },
    { name: "Rose", value: "rose" },
    { name: "Orange", value: "orange" },
    { name: "Amber", value: "amber" },
    { name: "Yellow", value: "yellow" },
    { name: "Lime", value: "lime" },
    { name: "Green", value: "green" },
    { name: "Emerald", value: "emerald" },
    { name: "Teal", value: "teal" },
    { name: "Cyan", value: "cyan" },
    { name: "Sky", value: "sky" },
    { name: "Blue", value: "blue" },
    { name: "Indigo", value: "indigo" },
    { name: "Violet", value: "violet" },
    { name: "Purple", value: "purple" },
    { name: "Fuchsia", value: "fuchsia" },
  ];

  // Get color class for the radio button
  const getColorClass = (color: string) => {
    const colorMap: Record<string, string> = {
      red: "bg-red-500",
      pink: "bg-pink-500",
      rose: "bg-rose-500",
      orange: "bg-orange-500",
      amber: "bg-amber-500",
      yellow: "bg-yellow-500",
      lime: "bg-lime-500",
      green: "bg-green-500",
      emerald: "bg-emerald-500",
      teal: "bg-teal-500",
      cyan: "bg-cyan-500",
      sky: "bg-sky-500",
      blue: "bg-blue-500",
      indigo: "bg-indigo-500",
      violet: "bg-violet-500",
      purple: "bg-purple-500",
      fuchsia: "bg-fuchsia-500",
    };
    
    return colorMap[color] || "bg-gray-500";
  };

  const handleCreate = () => {
    if (newMilestone.name.trim() === "") return;
    onCreate(newMilestone);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Milestone</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="milestone-title">Milestone Title</Label>
            <Input
              id="milestone-title"
              value={newMilestone.name}
              onChange={(e) => setNewMilestone({ ...newMilestone, name: e.target.value })}
              className="col-span-3"
              placeholder="Enter milestone title"
              autoFocus
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="milestone-description">Milestone Description</Label>
            <Textarea
              id="milestone-description"
              value={newMilestone.description}
              onChange={(e) => setNewMilestone({ ...newMilestone, description: e.target.value })}
              className="col-span-3"
              rows={4}
              placeholder="Enter milestone description (optional)"
            />
          </div>
          <div className="grid gap-2">
            <Label>Color</Label>
            <div className="grid grid-cols-6 gap-2">
              {colorOptions.map((color) => (
                <div key={color.value} className="flex items-center justify-center">
                  <button
                    type="button"
                    className={`w-8 h-8 rounded-full ${getColorClass(color.value)} flex items-center justify-center cursor-pointer transition-transform hover:scale-110 ${newMilestone.color === color.value ? 'ring-2 ring-offset-2 ring-offset-background ring-ring' : ''}`}
                    onClick={() => setNewMilestone({ ...newMilestone, color: color.value })}
                    aria-label={`Select ${color.name} color`}
                  >
                    {newMilestone.color === color.value && (
                      <Check className="h-4 w-4 text-white" />
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleCreate} disabled={newMilestone.name.trim() === ""}>
            Create
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
