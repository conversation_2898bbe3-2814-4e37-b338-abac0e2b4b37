"use client";

import { useState } from "react";
import { Milestone } from "@/lib/types";
import { Card, CardContent } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import MilestoneEditDialog from "@/components/MilestoneEditDialog";

interface MilestoneCardProps {
  milestone: Milestone;
  onUpdate: (updates: Partial<Milestone>) => void;
  onDelete: () => void;
}

export default function MilestoneCard({
  milestone,
  onUpdate,
  onDelete,
}: MilestoneCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Get border color based on milestone color
  const getBorderColor = () => {
    const colorMap: Record<string, string> = {
      red: "border-red-500",
      pink: "border-pink-500",
      rose: "border-rose-500",
      orange: "border-orange-500",
      amber: "border-amber-500",
      yellow: "border-yellow-500",
      lime: "border-lime-500",
      green: "border-green-500",
      emerald: "border-emerald-500",
      teal: "border-teal-500",
      cyan: "border-cyan-500",
      sky: "border-sky-500",
      blue: "border-blue-500",
      indigo: "border-indigo-500",
      violet: "border-violet-500",
      purple: "border-purple-500",
      fuchsia: "border-fuchsia-500",
    };
    
    return colorMap[milestone.color] || "border-gray-500";
  };

  const handleCardClick = () => {
    setIsDialogOpen(true);
  };

  return (
    <>
      <Card
        className={`w-[280px] sm:w-[320px] md:w-[360px] lg:w-[400px] rounded-2xl shadow-xl ${getBorderColor()} border-t-4 cursor-pointer hover:shadow-2xl transition-shadow bg-milestone-card`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onTouchStart={() => setIsHovered(true)}
        onTouchEnd={() => setTimeout(() => setIsHovered(false), 1000)}
        onClick={handleCardClick}
      >
        <CardContent className="p-4 relative">
          <div className="text-base md:text-lg font-medium text-center mb-2">
            {milestone.name}
          </div>

          {/* Display description with truncation */}
          {milestone.description && (
            <div className="text-xs text-muted-foreground/80 dark:text-muted-foreground/90 line-clamp-2 overflow-hidden text-ellipsis text-center">
              {milestone.description}
            </div>
          )}
          
          {isHovered && (
            <button
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click
                onDelete();
              }}
              className="absolute top-1 sm:top-2 right-1 sm:right-2 text-gray-500 hover:text-red-500 transition-colors"
              aria-label="Delete milestone"
            >
              <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
            </button>
          )}
        </CardContent>
      </Card>

      <MilestoneEditDialog
        milestone={milestone}
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onUpdate={onUpdate}
      />
    </>
  );
}
