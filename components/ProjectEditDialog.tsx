"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { ProjectStatus, PROJECT_STATUS_OPTIONS, ProjectPriority, PROJECT_PRIORITY_OPTIONS } from "@/types/project";

interface ProjectEditDialogProps {
  projectName: string;
  projectDescription: string;
  projectStatus?: ProjectStatus;
  projectPriority?: ProjectPriority;
  projectTeam?: string;
  projectLead?: string;
  projectStartDate?: number;
  projectEndDate?: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (name: string, description: string, status: ProjectStatus, priority: ProjectPriority, team?: string, lead?: string, startDate?: number, endDate?: number) => Promise<void>;
  isUpdating?: boolean;
}

export default function ProjectEditDialog({
  projectName,
  projectDescription,
  projectStatus = "Backlog",
  projectPriority = "No priority",
  projectTeam = "",
  projectLead = "",
  projectStartDate,
  projectEndDate,
  open,
  onOpenChange,
  onUpdate,
  isUpdating,
}: ProjectEditDialogProps) {
  const [editedName, setEditedName] = useState(projectName);
  const [editedDescription, setEditedDescription] = useState(projectDescription);
  const [editedStatus, setEditedStatus] = useState<ProjectStatus>(projectStatus);
  const [editedPriority, setEditedPriority] = useState<ProjectPriority>(projectPriority);
  const [editedTeam, setEditedTeam] = useState(projectTeam);
  const [editedLead, setEditedLead] = useState(projectLead);
  const [editedStartDate, setEditedStartDate] = useState<number | undefined>(projectStartDate);
  const [editedEndDate, setEditedEndDate] = useState<number | undefined>(projectEndDate);
  const [dateError, setDateError] = useState<string | null>(null);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setEditedName(projectName);
      setEditedDescription(projectDescription);
      setEditedStatus(projectStatus);
      setEditedPriority(projectPriority);
      setEditedTeam(projectTeam);
      setEditedLead(projectLead);
      setEditedStartDate(projectStartDate);
      setEditedEndDate(projectEndDate);
      setDateError(null);
    }
  }, [open, projectName, projectDescription, projectStatus, projectPriority, projectTeam, projectLead, projectStartDate, projectEndDate]);

  const handleSave = async () => {
    // Validate dates
    if (editedStartDate && editedEndDate && editedEndDate < editedStartDate) {
      setDateError("End date must be after start date");
      return;
    }

    if (editedName.trim() !== "") {
      try {
        await onUpdate(
          editedName,
          editedDescription,
          editedStatus,
          editedPriority,
          editedTeam,
          editedLead,
          editedStartDate,
          editedEndDate
        );
      } catch (error) {
        // Error handling is done in ProjectBoard, but catch here to prevent unhandled rejection
        console.error("Error during onUpdate in ProjectEditDialog:", error);
        // Optionally, keep dialog open on error by not calling onOpenChange(false)
        // For now, we'll close it as per original behavior, but after await.
      }
    }
    // Close the dialog only after onUpdate has resolved (or if name is empty)
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Project</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="project-name">Project Name</Label>
            <Input
              id="project-name"
              value={editedName}
              onChange={(e) => setEditedName(e.target.value)}
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="project-description">Project Description</Label>
            <Textarea
              id="project-description"
              value={editedDescription}
              onChange={(e) => setEditedDescription(e.target.value)}
              className="min-h-[100px]"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="project-status">Status</Label>
            <Select value={editedStatus} onValueChange={(value: ProjectStatus) => setEditedStatus(value)}>
              <SelectTrigger id="project-status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {PROJECT_STATUS_OPTIONS.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="project-priority">Priority</Label>
            <Select value={editedPriority} onValueChange={(value: ProjectPriority) => setEditedPriority(value)}>
              <SelectTrigger id="project-priority">
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                {PROJECT_PRIORITY_OPTIONS.map((priority) => (
                  <SelectItem key={priority} value={priority}>
                    {priority}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="project-team">Team (Optional)</Label>
            <Input
              id="project-team"
              value={editedTeam}
              onChange={(e) => setEditedTeam(e.target.value)}
              placeholder="Project team"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="project-lead">Project Lead (Optional)</Label>
            <Input
              id="project-lead"
              value={editedLead}
              onChange={(e) => setEditedLead(e.target.value)}
              placeholder="Project lead"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="project-start-date">Start Date (Optional)</Label>
              <Input
                id="project-start-date"
                type="date"
                value={editedStartDate ? new Date(editedStartDate).toISOString().split('T')[0] : ''}
                onChange={(e) => {
                  const date = e.target.value ? new Date(e.target.value).getTime() : undefined;
                  setEditedStartDate(date);
                  setDateError(null);
                }}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="project-end-date">End Date (Optional)</Label>
              <Input
                id="project-end-date"
                type="date"
                value={editedEndDate ? new Date(editedEndDate).toISOString().split('T')[0] : ''}
                onChange={(e) => {
                  const date = e.target.value ? new Date(e.target.value).getTime() : undefined;
                  setEditedEndDate(date);
                  setDateError(null);
                }}
              />
            </div>
            {dateError && <p className="col-span-2 text-sm text-red-500">{dateError}</p>}
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isUpdating}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isUpdating || editedName.trim() === ""}>
            {isUpdating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
