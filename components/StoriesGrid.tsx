"use client";

import { Milestone, Story } from "@/lib/types";
import StoryColumn from "@/components/StoryColumn";

interface StoriesGridProps {
  milestones: Milestone[];
  onAddStory: (milestoneId: string) => void;
  onUpdateStory: (milestoneId: string, storyId: string, updates: Partial<Story>) => void;
  onDeleteStory: (milestoneId: string, storyId: string) => void;
}

export default function StoriesGrid({
  milestones,
  onAddStory,
  onUpdateStory,
  onDeleteStory,
}: StoriesGridProps) {
  return (
    <div className="w-full flex-1">
      <div className="flex gap-6 h-full pb-4 px-2">
        {milestones.map((milestone) => (
          <StoryColumn
            key={milestone.id}
            milestone={milestone}
            milestones={milestones}
            onAddStory={() => onAddStory(milestone.id)}
            onUpdateStory={(storyId: string, updates: Partial<Story>) => onUpdateStory(milestone.id, storyId, updates)}
            onDeleteStory={(storyId: string) => onDeleteStory(milestone.id, storyId)}
          />
        ))}
      </div>
    </div>
  );
}
