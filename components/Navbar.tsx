"use client";

import Link from 'next/link';
import Image from 'next/image'; // Import next/image
import { User<PERSON>utton, SignInButton, SignUpButton } from '@clerk/nextjs';
import { Authenticated, Unauthenticated } from 'convex/react';
import { ThemeToggle } from '@/components/theme-toggle';
import { Button } from '@/components/ui/button';

export default function Navbar() {
  return (
    <nav className="bg-background border-b sticky top-0 z-50">
      <div className="w-full px-4 h-16 flex items-center justify-between">
        <Link href="/" className="flex items-center text-2xl font-bold text-primary">
          <Image 
            src="/flowdeck-logo-icon.svg" 
            alt="FlowDeck Icon" 
            width={28} 
            height={28} 
            className="mr-2" 
            priority
          />
          <span>FlowDeck</span>
        </Link>
        <div className="flex items-center space-x-4">
          <ThemeToggle />
          <Authenticated>
            <UserButton afterSignOutUrl="/" />
          </Authenticated>
          <Unauthenticated>
            <SignInButton mode="modal">
              <Button variant="outline" size="sm">Sign In</Button>
            </SignInButton>
            <SignUpButton mode="modal">
              <Button size="sm">Sign Up</Button>
            </SignUpButton>
          </Unauthenticated>
        </div>
      </div>
    </nav>
  );
}
