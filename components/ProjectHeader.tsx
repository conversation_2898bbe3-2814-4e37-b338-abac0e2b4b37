"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Pencil, Users, User, Calendar, Download } from "lucide-react";
import ProjectEditDialog from "./ProjectEditDialog";
import { ProjectStatus, getStatusColor, ProjectPriority, getPriorityColor } from "@/types/project";

interface ProjectHeaderProps {
  projectName: string;
  projectDescription: string;
  projectStatus?: ProjectStatus;
  projectPriority?: ProjectPriority;
  projectTeam?: string;
  projectLead?: string;
  projectStartDate?: number;
  projectEndDate?: number;
  onUpdateProject: (name: string, description: string, status: ProjectStatus, priority: ProjectPriority, team?: string, lead?: string, startDate?: number, endDate?: number) => Promise<void>;
  onExportProject?: () => void;
  isUpdating?: boolean;
}

export default function ProjectHeader({
  projectName,
  projectDescription,
  projectStatus = "Backlog",
  projectPriority = "No priority",
  projectTeam,
  projectLead,
  projectStartDate,
  projectEndDate,
  onUpdateProject,
  onExportProject,
  isUpdating,
}: ProjectHeaderProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleOpenDialog = () => {
    setIsDialogOpen(true);
  };

  const handleExport = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent opening the edit dialog
    onExportProject?.();
  };

  return (
    <div className="flex flex-col items-center w-full space-y-2">
      <Card
        className="w-full max-w-3xl cursor-pointer hover:shadow-md transition-shadow duration-200 group"
        onClick={handleOpenDialog}
      >
        <CardContent className="p-3 relative">
          {/* Header section with title and export button */}
          <div className="flex items-start justify-between mb-3">
            <h1 className="text-xl sm:text-xl md:text-2xl font-bold text-left flex-1 pr-4">
              {projectName}
            </h1>
            <div className="flex items-center gap-2 flex-shrink-0">
              {onExportProject && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExport}
                  className="flex items-center"
                >
                  <Download className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
          
          {/* Description */}
          <p className="text-sm text-muted-foreground mb-4 text-left">
            {projectDescription}
          </p>
          
          {/* Team information and status/priority */}
          {(projectTeam || projectLead || projectStartDate || projectStatus || projectPriority) && (
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center gap-6">
                {projectTeam && (
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>Team: {projectTeam}</span>
                  </div>
                )}
                {projectLead && (
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    <span>Lead: {projectLead}</span>
                  </div>
                )}
                {projectStartDate && (
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>Start: {new Date(projectStartDate).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded-md text-xs font-medium ${getStatusColor(projectStatus)}`}>
                  {projectStatus}
                </span>
                <span className={`px-2 py-1 rounded-md text-xs font-medium ${getPriorityColor(projectPriority)}`}>
                  {projectPriority}
                </span>
              </div>
            </div>
          )}
          

        </CardContent>
      </Card>
      
      <ProjectEditDialog
        projectName={projectName}
        projectDescription={projectDescription}
        projectStatus={projectStatus}
        projectPriority={projectPriority}
        projectTeam={projectTeam}
        projectLead={projectLead}
        projectStartDate={projectStartDate}
        projectEndDate={projectEndDate}
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onUpdate={onUpdateProject}
        isUpdating={isUpdating}
      />
    </div>
  );
}
