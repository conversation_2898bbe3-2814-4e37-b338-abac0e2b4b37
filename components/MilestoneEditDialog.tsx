"use client";

import { useState } from "react";
import { Milestone } from "@/lib/types";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface MilestoneEditDialogProps {
  milestone: Milestone;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (updates: Partial<Milestone>) => void;
}

export default function MilestoneEditDialog({
  milestone,
  open,
  onOpenChange,
  onUpdate,
}: MilestoneEditDialogProps) {
  const [editedMilestone, setEditedMilestone] = useState<Partial<Milestone>>({
    name: milestone.name,
    description: milestone.description || "",
    color: milestone.color,
  });

  // Define a wider assortment of colors
  const colorOptions = [
    { name: "Red", value: "red" },
    { name: "Pink", value: "pink" },
    { name: "<PERSON>", value: "rose" },
    { name: "Orange", value: "orange" },
    { name: "Amber", value: "amber" },
    { name: "Yellow", value: "yellow" },
    { name: "Lime", value: "lime" },
    { name: "Green", value: "green" },
    { name: "Emerald", value: "emerald" },
    { name: "Teal", value: "teal" },
    { name: "Cyan", value: "cyan" },
    { name: "Sky", value: "sky" },
    { name: "Blue", value: "blue" },
    { name: "Indigo", value: "indigo" },
    { name: "Violet", value: "violet" },
    { name: "Purple", value: "purple" },
    { name: "Fuchsia", value: "fuchsia" },
  ];

  // Get color class for the radio button
  const getColorClass = (color: string) => {
    const colorMap: Record<string, string> = {
      red: "bg-red-500",
      pink: "bg-pink-500",
      rose: "bg-rose-500",
      orange: "bg-orange-500",
      amber: "bg-amber-500",
      yellow: "bg-yellow-500",
      lime: "bg-lime-500",
      green: "bg-green-500",
      emerald: "bg-emerald-500",
      teal: "bg-teal-500",
      cyan: "bg-cyan-500",
      sky: "bg-sky-500",
      blue: "bg-blue-500",
      indigo: "bg-indigo-500",
      violet: "bg-violet-500",
      purple: "bg-purple-500",
      fuchsia: "bg-fuchsia-500",
    };
    
    return colorMap[color] || "bg-gray-500";
  };

  const handleSave = () => {
    onUpdate(editedMilestone);
    onOpenChange(false);
  };

  const handleCancel = () => {
    // Reset form and close dialog
    setEditedMilestone({
      name: milestone.name,
      description: milestone.description || "",
      color: milestone.color,
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Milestone</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="milestone-title">Milestone Title</Label>
            <Input
              id="milestone-title"
              value={editedMilestone.name}
              onChange={(e) => setEditedMilestone({ ...editedMilestone, name: e.target.value })}
              className="col-span-3"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="milestone-description">Milestone Description</Label>
            <Textarea
              id="milestone-description"
              value={editedMilestone.description}
              onChange={(e) => setEditedMilestone({ ...editedMilestone, description: e.target.value })}
              className="col-span-3"
              rows={4}
            />
          </div>
          <div className="grid gap-2">
            <Label>Color</Label>
            <div className="grid grid-cols-6 gap-2">
              {colorOptions.map((color) => (
                <div key={color.value} className="flex items-center justify-center">
                  <button
                    type="button"
                    className={`w-8 h-8 rounded-full ${getColorClass(color.value)} flex items-center justify-center cursor-pointer transition-transform hover:scale-110 ${editedMilestone.color === color.value ? 'ring-2 ring-offset-2 ring-offset-background ring-ring' : ''}`}
                    onClick={() => setEditedMilestone({ ...editedMilestone, color: color.value })}
                    aria-label={`Select ${color.name} color`}
                  >
                    {editedMilestone.color === color.value && (
                      <Check className="h-4 w-4 text-white" />
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
