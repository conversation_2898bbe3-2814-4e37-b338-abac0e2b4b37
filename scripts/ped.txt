Overview

A modern project planning web application that allows users to manage a project by organizing milestones and their associated stories (tasks). The interface is based on a Miro-style planning board, not Kanban.

Key Features & User Flow

Editable project name at the top.

Horizontally scrolling row of milestones, each represented as a card (color-coded).

Under each milestone, a column of story cards (editable task/story titles).

Add/remove milestones and stories.

Inline editing for all names (project, milestone, story).

(Optional: drag-and-drop stories between milestones).

Responsive and visually clean interface.

Supports both light and dark mode.

Component Architecture

ProjectBoard: Receives project data (name, milestones, stories). Houses all subcomponents.

ProjectHeader: Editable project name (Input from shadcn/ui).

MilestonesRow: Displays a row of MilestoneCard components. Includes "Add Milestone" button.

MilestoneCard: Shows milestone name (editable). Has colored border/accent (Tailwind: border-[color]-500). Delete milestone option.

StoriesGrid: Renders a column (StoryColumn) for each milestone.

StoryColumn: Contains StoryCard components for each story. "Add Story" button at bottom.

StoryCard: Editable story name/title. Delete story option.

Data Model

type Project = {
  id: string;
  name: string;
  milestones: Milestone[];
};

type Milestone = {
  id: string;
  name: string;
  color: string; // e.g. "yellow", "blue"
  stories: Story[];
};

type Story = {
  id: string;
  name: string;
};


UI/UX Requirements

Use shadcn/ui for Cards, Inputs, and Buttons.

Use Tailwind CSS for layout, spacing, color, and responsiveness.

All editing should be inline (no popups for editing names).

Cards and columns should have rounded-2xl corners, soft shadows (shadow-xl), and adequate padding.

Responsive design: works on desktop and mobile/tablet.

Light and dark mode support (use system preference).

Minimal, modern look—avoid clutter.

Only show controls for add/remove when hovered or focused for clean UI.

(Optional/future) Drag-and-drop for stories between milestones.

Technical Notes

Next.js 15, app directory structure, TypeScript.

All state is local for now (use React state/hooks).

Do not use Kanban, Agile, or Scrum terminology anywhere.

Milestones = columns, stories = cards within columns.

No backend, deployable as static or serverless if needed.