import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  projects: defineTable({
    userId: v.string(),
    name: v.string(),
    description: v.optional(v.string()),
    status: v.optional(v.union(
      v.literal("Backlog"),
      v.literal("Planned"),
      v.literal("Paused"),
      v.literal("Next up"),
      v.literal("Completed"),
      v.literal("Canceled")
    )),
    priority: v.optional(v.union(
      v.literal("No priority"),
      v.literal("Urgent"),
      v.literal("High"),
      v.literal("Medium"),
      v.literal("Low")
    )),
    team: v.optional(v.string()),
    lead: v.optional(v.string()),
    startDate: v.optional(v.number()), // Unix timestamp in milliseconds
    endDate: v.optional(v.number()), // Unix timestamp in milliseconds
  })
  .index("by_user", ["userId"]),

  milestones: defineTable({
    projectId: v.id("projects"),
    name: v.string(),
    description: v.optional(v.string()),
    color: v.optional(v.string()),
    order: v.optional(v.number()),
  })
  .index("by_project", ["projectId"]),

  stories: defineTable({
    milestoneId: v.id("milestones"),
    projectId: v.id("projects"), // Denormalized for easier querying
    name: v.string(),
    background: v.optional(v.string()),
    acceptanceCriteria: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    storyPoints: v.optional(v.number()),
    order: v.optional(v.number()), // For ordering stories within a milestone column
  })
  .index("by_milestone", ["milestoneId"])
  .index("by_project", ["projectId"]),
});
