import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createMilestone = mutation({
  args: {
    projectId: v.id("projects"),
    name: v.string(),
    description: v.optional(v.string()),
    color: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      throw new Error("User is not authenticated. Cannot create milestone.");
    }

    // Get the project to verify ownership
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Project not found.");
    }

    // Optional: Check if the user owns the project
    // if (project.userId !== identity.subject) {
    //   throw new Error("User is not authorized to create milestones for this project.");
    // }

    // Get the highest order value to place the new milestone at the end
    const milestones = await ctx.db
      .query("milestones")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();
    
    const maxOrder = milestones.length > 0
      ? Math.max(...milestones.map(m => m.order ?? 0))
      : -1;
    
    const milestoneId = await ctx.db.insert("milestones", {
      projectId: args.projectId,
      name: args.name,
      description: args.description,
      color: args.color,
      order: maxOrder + 1,
    });

    return milestoneId;
  },
});

export const getByProject = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    // Get all milestones for a specific project, ordered by the order field
    const milestones = await ctx.db
      .query("milestones")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();
    
    // Sort by order field
    return milestones.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
  },
});

export const updateMilestone = mutation({
  args: {
    milestoneId: v.id("milestones"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    color: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated. Cannot update milestone.");
    }

    const milestone = await ctx.db.get(args.milestoneId);
    if (!milestone) {
      throw new Error("Milestone not found.");
    }

    // Optional: Verify project ownership
    // const project = await ctx.db.get(milestone.projectId);
    // if (!project || project.userId !== identity.subject) {
    //   throw new Error("User is not authorized to update this milestone.");
    // }

    // Create an update object with only the fields that were provided
    const updateFields: Record<string, any> = {};
    if (args.name !== undefined) updateFields.name = args.name;
    if (args.description !== undefined) updateFields.description = args.description;
    if (args.color !== undefined) updateFields.color = args.color;

    await ctx.db.patch(args.milestoneId, updateFields);

    return await ctx.db.get(args.milestoneId);
  },
});

export const deleteMilestone = mutation({
  args: { milestoneId: v.id("milestones") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated. Cannot delete milestone.");
    }

    const milestone = await ctx.db.get(args.milestoneId);
    if (!milestone) {
      throw new Error("Milestone not found.");
    }

    // Optional: Verify project ownership
    // const project = await ctx.db.get(milestone.projectId);
    // if (!project || project.userId !== identity.subject) {
    //   throw new Error("User is not authorized to delete this milestone.");
    // }

    // Get all stories associated with this milestone
    const stories = await ctx.db
      .query("stories")
      .withIndex("by_milestone", (q) => q.eq("milestoneId", args.milestoneId))
      .collect();

    // Delete all associated stories first
    for (const story of stories) {
      await ctx.db.delete(story._id);
    }

    // Then delete the milestone
    await ctx.db.delete(args.milestoneId);

    // Optionally reorder remaining milestones
    const remainingMilestones = await ctx.db
      .query("milestones")
      .withIndex("by_project", (q) => q.eq("projectId", milestone.projectId))
      .collect();
    
    // Sort by current order
    const sortedMilestones = remainingMilestones.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
    
    // Update order values to be sequential
    for (let i = 0; i < sortedMilestones.length; i++) {
      await ctx.db.patch(sortedMilestones[i]._id, { order: i });
    }

    return true;
  },
});

export const updateMilestoneOrder = mutation({
  args: {
    milestoneId: v.id("milestones"),
    newOrder: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated. Cannot reorder milestones.");
    }

    const milestone = await ctx.db.get(args.milestoneId);
    if (!milestone) {
      throw new Error("Milestone not found.");
    }

    // Optional: Verify project ownership
    // const project = await ctx.db.get(milestone.projectId);
    // if (!project || project.userId !== identity.subject) {
    //   throw new Error("User is not authorized to reorder milestones for this project.");
    // }

    // Get all milestones for this project
    const milestones = await ctx.db
      .query("milestones")
      .withIndex("by_project", (q) => q.eq("projectId", milestone.projectId))
      .collect();
    
    // Sort by current order
    const sortedMilestones = milestones.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
    
    // Remove the milestone being moved
    const filteredMilestones = sortedMilestones.filter(m => m._id.toString() !== args.milestoneId.toString());
    
    // Insert the milestone at the new position
    filteredMilestones.splice(args.newOrder, 0, milestone);
    
    // Update all order values
    for (let i = 0; i < filteredMilestones.length; i++) {
      await ctx.db.patch(filteredMilestones[i]._id, { order: i });
    }

    return true;
  },
});
