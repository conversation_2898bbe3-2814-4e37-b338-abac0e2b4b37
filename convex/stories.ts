import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createStory = mutation({
  args: {
    milestoneId: v.id("milestones"),
    name: v.string(),
    background: v.optional(v.string()),
    acceptanceCriteria: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    storyPoints: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      throw new Error("User is not authenticated. Cannot create story.");
    }

    // Get the milestone to verify it exists and to get the projectId
    const milestone = await ctx.db.get(args.milestoneId);
    if (!milestone) {
      throw new Error("Milestone not found.");
    }

    // Optional: Verify project ownership
    // const project = await ctx.db.get(milestone.projectId);
    // if (!project || project.userId !== identity.subject) {
    //   throw new Error("User is not authorized to create stories for this milestone.");
    // }

    // Get the highest order value to place the new story at the end
    const stories = await ctx.db
      .query("stories")
      .withIndex("by_milestone", (q) => q.eq("milestoneId", args.milestoneId))
      .collect();
    
    const maxOrder = stories.length > 0
      ? Math.max(...stories.map(s => s.order ?? 0))
      : -1;
    
    const storyId = await ctx.db.insert("stories", {
      milestoneId: args.milestoneId,
      projectId: milestone.projectId, // Denormalized for easier querying
      name: args.name,
      background: args.background,
      acceptanceCriteria: args.acceptanceCriteria,
      tags: args.tags,
      storyPoints: args.storyPoints,
      order: maxOrder + 1,
    });

    return storyId;
  },
});

export const getByMilestone = query({
  args: { milestoneId: v.id("milestones") },
  handler: async (ctx, args) => {
    // Get all stories for a specific milestone, ordered by the order field
    const stories = await ctx.db
      .query("stories")
      .withIndex("by_milestone", (q) => q.eq("milestoneId", args.milestoneId))
      .collect();
    
    // Sort by order field
    return stories.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
  },
});

export const getByProject = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    // Get all stories for a specific project
    const stories = await ctx.db
      .query("stories")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();
    
    return stories;
  },
});

export const updateStory = mutation({
  args: {
    storyId: v.id("stories"),
    name: v.optional(v.string()),
    background: v.optional(v.string()),
    acceptanceCriteria: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    storyPoints: v.optional(v.number()),
    milestoneId: v.optional(v.id("milestones")), // Allow moving to different milestone
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated. Cannot update story.");
    }

    const story = await ctx.db.get(args.storyId);
    if (!story) {
      throw new Error("Story not found.");
    }

    // Optional: Verify project ownership
    // const project = await ctx.db.get(story.projectId);
    // if (!project || project.userId !== identity.subject) {
    //   throw new Error("User is not authorized to update this story.");
    // }

    // If moving to a different milestone, verify the milestone exists and is in the same project
    if (args.milestoneId && args.milestoneId.toString() !== story.milestoneId.toString()) {
      const newMilestone = await ctx.db.get(args.milestoneId);
      if (!newMilestone) {
        throw new Error("Target milestone not found.");
      }
      
      if (newMilestone.projectId.toString() !== story.projectId.toString()) {
        throw new Error("Cannot move story to a milestone in a different project.");
      }
    }

    // Create an update object with only the fields that were provided
    const updateFields: Record<string, any> = {};
    if (args.name !== undefined) updateFields.name = args.name;
    if (args.background !== undefined) updateFields.background = args.background;
    if (args.acceptanceCriteria !== undefined) updateFields.acceptanceCriteria = args.acceptanceCriteria;
    if (args.tags !== undefined) updateFields.tags = args.tags;
    if (args.storyPoints !== undefined) updateFields.storyPoints = args.storyPoints;
    if (args.milestoneId !== undefined) updateFields.milestoneId = args.milestoneId;

    await ctx.db.patch(args.storyId, updateFields);

    return await ctx.db.get(args.storyId);
  },
});

export const deleteStory = mutation({
  args: { storyId: v.id("stories") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated. Cannot delete story.");
    }

    const story = await ctx.db.get(args.storyId);
    if (!story) {
      throw new Error("Story not found.");
    }

    // Optional: Verify project ownership
    // const project = await ctx.db.get(story.projectId);
    // if (!project || project.userId !== identity.subject) {
    //   throw new Error("User is not authorized to delete this story.");
    // }

    // Delete the story
    await ctx.db.delete(args.storyId);

    // Optionally reorder remaining stories in the same milestone
    const remainingStories = await ctx.db
      .query("stories")
      .withIndex("by_milestone", (q) => q.eq("milestoneId", story.milestoneId))
      .collect();
    
    // Sort by current order
    const sortedStories = remainingStories.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
    
    // Update order values to be sequential
    for (let i = 0; i < sortedStories.length; i++) {
      await ctx.db.patch(sortedStories[i]._id, { order: i });
    }

    return true;
  },
});

export const updateStoryOrder = mutation({
  args: {
    storyId: v.id("stories"),
    newMilestoneId: v.optional(v.id("milestones")),
    newOrder: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated. Cannot reorder stories.");
    }

    const story = await ctx.db.get(args.storyId);
    if (!story) {
      throw new Error("Story not found.");
    }

    // Optional: Verify project ownership
    // const project = await ctx.db.get(story.projectId);
    // if (!project || project.userId !== identity.subject) {
    //   throw new Error("User is not authorized to reorder stories for this project.");
    // }

    // Determine if we're moving within the same milestone or to a different one
    const targetMilestoneId = args.newMilestoneId || story.milestoneId;
    
    // If moving to a different milestone, verify it exists and is in the same project
    if (args.newMilestoneId && args.newMilestoneId.toString() !== story.milestoneId.toString()) {
      const newMilestone = await ctx.db.get(args.newMilestoneId);
      if (!newMilestone) {
        throw new Error("Target milestone not found.");
      }
      
      if (newMilestone.projectId.toString() !== story.projectId.toString()) {
        throw new Error("Cannot move story to a milestone in a different project.");
      }
    }

    // Get all stories for the target milestone
    const stories = await ctx.db
      .query("stories")
      .withIndex("by_milestone", (q) => q.eq("milestoneId", targetMilestoneId))
      .collect();
    
    // Sort by current order
    const sortedStories = stories.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
    
    // If moving within the same milestone, remove the story being moved
    let filteredStories;
    if (targetMilestoneId.toString() === story.milestoneId.toString()) {
      filteredStories = sortedStories.filter(s => s._id.toString() !== args.storyId.toString());
    } else {
      // If moving to a different milestone, keep all stories in the target milestone
      filteredStories = sortedStories;
      // Update the story's milestoneId
      await ctx.db.patch(args.storyId, { milestoneId: targetMilestoneId });
    }
    
    // Insert the story at the new position
    filteredStories.splice(args.newOrder, 0, story);
    
    // Update all order values
    for (let i = 0; i < filteredStories.length; i++) {
      await ctx.db.patch(filteredStories[i]._id, { order: i });
    }

    // If we moved to a different milestone, we may need to reorder the original milestone too
    if (args.newMilestoneId && args.newMilestoneId.toString() !== story.milestoneId.toString()) {
      const originalMilestoneStories = await ctx.db
        .query("stories")
        .withIndex("by_milestone", (q) => q.eq("milestoneId", story.milestoneId))
        .collect();
      
      // Sort by current order
      const sortedOriginalStories = originalMilestoneStories.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
      
      // Update all order values
      for (let i = 0; i < sortedOriginalStories.length; i++) {
        await ctx.db.patch(sortedOriginalStories[i]._id, { order: i });
      }
    }

    return true;
  },
});
