import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createProject = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    status: v.optional(v.union(
      v.literal("Backlog"),
      v.literal("Planned"),
      v.literal("Paused"),
      v.literal("Next up"),
      v.literal("Completed"),
      v.literal("Canceled")
    )),
    priority: v.optional(v.union(
      v.literal("No priority"),
      v.literal("Urgent"),
      v.literal("High"),
      v.literal("Medium"),
      v.literal("Low")
    )),
    team: v.optional(v.string()),
    lead: v.optional(v.string()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      throw new Error("User is not authenticated. Cannot create project.");
    }

    // The 'subject' property from Clerk identity is the Clerk User ID
    const userId = identity.subject;

    const projectId = await ctx.db.insert("projects", {
      userId: userId,
      name: args.name,
      description: args.description,
      status: args.status || "Backlog", // Default to "Backlog" if not provided
      priority: args.priority || "No priority", // Default to "No priority" if not provided
      team: args.team,
      lead: args.lead,
      startDate: args.startDate,
      endDate: args.endDate,
    });

    return projectId;
  },
});

export const get = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      // If the user is not authenticated, return no projects
      return [];
    }

    const userId = identity.subject;

    // Query the 'projects' table using the 'by_user' index
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    return projects;
  },
});

export const deleteProject = mutation({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated. Cannot delete project.");
    }

    // Optional: Check if the user owns the project before deleting
    // const project = await ctx.db.get(args.projectId);
    // if (!project || project.userId !== identity.subject) {
    //   throw new Error("User is not authorized to delete this project or project not found.");
    // }

    // Get all milestones associated with this project
    const milestones = await ctx.db
      .query("milestones")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    // Delete all stories associated with each milestone
    for (const milestone of milestones) {
      const stories = await ctx.db
        .query("stories")
        .withIndex("by_milestone", (q) => q.eq("milestoneId", milestone._id))
        .collect();

      // Delete all stories for this milestone
      for (const story of stories) {
        await ctx.db.delete(story._id);
      }

      // Delete the milestone
      await ctx.db.delete(milestone._id);
    }

    // Finally, delete the project itself
    await ctx.db.delete(args.projectId);
    
    return true;
  },
});

export const updateProjectDetails = mutation({
  args: {
    projectId: v.id("projects"),
    name: v.string(),
    description: v.optional(v.string()),
    status: v.optional(v.union(
      v.literal("Backlog"),
      v.literal("Planned"),
      v.literal("Paused"),
      v.literal("Next up"),
      v.literal("Completed"),
      v.literal("Canceled")
    )),
    priority: v.optional(v.union(
      v.literal("No priority"),
      v.literal("Urgent"),
      v.literal("High"),
      v.literal("Medium"),
      v.literal("Low")
    )),
    team: v.optional(v.string()),
    lead: v.optional(v.string()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated. Cannot update project details.");
    }

    // Ensure the user owns the project or has permission (if such logic exists)
    // For now, we assume if they can fetch it, they can update basic details.
    // const project = await ctx.db.get(args.projectId);
    // if (project?.userId !== identity.subject) {
    //   throw new Error("User is not authorized to update this project.");
    // }

    await ctx.db.patch(args.projectId, {
      name: args.name,
      description: args.description,
      status: args.status || "Backlog", // Default to "Backlog" if not provided
      priority: args.priority || "No priority", // Default to "No priority" if not provided
      team: args.team,
      lead: args.lead,
      startDate: args.startDate,
      endDate: args.endDate,
    });

    // Optionally, return the updated project or a success status
    // return await ctx.db.get(args.projectId);
  },
});

export const getById = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    // Allow public access to a project page, or enforce auth if needed
    // if (!identity) {
    //   throw new Error("User is not authenticated.");
    // }

    const project = await ctx.db.get(args.projectId);

    if (!project) {
      // Consider how to handle project not found - return null or throw error
      // Returning null might be better for the frontend to handle gracefully
      return null;
    }

    // Optional: If projects are private, verify ownership
    // if (project.userId !== identity?.subject) {
    //   // Or if you allow public viewing but want to restrict for non-owners
    //   return null; // Or throw an authorization error
    // }

    return project;
  },
});
